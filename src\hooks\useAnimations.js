import { useEffect, useRef, useCallback } from 'react';
import { 
  animate, 
  createSequence, 
  headerAnimations, 
  animationUtils 
} from '../lib/animations';

/**
 * Custom React hook for GSAP animations
 * Provides easy access to animations with proper cleanup and lifecycle management
 */
export const useAnimations = () => {
  const timelinesRef = useRef([]);

  /**
   * Clean up all animations when component unmounts
   */
  useEffect(() => {
    return () => {
      // Kill all timelines on unmount
      timelinesRef.current.forEach(timeline => {
        if (timeline && timeline.kill) {
          timeline.kill();
        }
      });
      timelinesRef.current = [];
    };
  }, []);

  /**
   * Add timeline to tracking array
   */
  const trackTimeline = useCallback((timeline) => {
    if (timeline) {
      timelinesRef.current.push(timeline);
    }
    return timeline;
  }, []);

  /**
   * Basic animation function
   */
  const animateElements = useCallback((elements, animationType, config = {}) => {
    const timeline = animate(elements, animationType, config);
    return trackTimeline(timeline);
  }, [trackTimeline]);

  /**
   * Create animation sequence
   */
  const createAnimationSequence = useCallback((animationSequence) => {
    const timeline = createSequence(animationSequence);
    return trackTimeline(timeline);
  }, [trackTimeline]);

  /**
   * Header-specific animations
   */
  const animateHeader = useCallback((selectors, config = {}) => {
    const timeline = headerAnimations.animateHeaderSequence(selectors, config);
    return trackTimeline(timeline);
  }, [trackTimeline]);

  const animateH1FromSides = useCallback((leftElement, rightElement = null, config = {}) => {
    const timeline = headerAnimations.animateH1FromSides(leftElement, rightElement, config);
    return trackTimeline(timeline);
  }, [trackTimeline]);

  const animateContentFromBottom = useCallback((elements, config = {}) => {
    const timeline = headerAnimations.animateContentFromBottom(elements, config);
    return trackTimeline(timeline);
  }, [trackTimeline]);

  /**
   * Utility functions
   */
  const killAnimations = useCallback((elements) => {
    animationUtils.killAnimations(elements);
  }, []);

  const resetElements = useCallback((elements) => {
    animationUtils.resetElements(elements);
  }, []);

  const createScrollAnimation = useCallback((trigger, elements, animationType, config = {}) => {
    const timeline = animationUtils.createScrollAnimation(trigger, elements, animationType, config);
    return trackTimeline(timeline);
  }, [trackTimeline]);

  const batchAnimate = useCallback((animationBatch) => {
    const timeline = animationUtils.batchAnimate(animationBatch);
    return trackTimeline(timeline);
  }, [trackTimeline]);

  /**
   * Kill all tracked timelines
   */
  const killAllAnimations = useCallback(() => {
    timelinesRef.current.forEach(timeline => {
      if (timeline && timeline.kill) {
        timeline.kill();
      }
    });
    timelinesRef.current = [];
  }, []);

  return {
    // Basic animations
    animate: animateElements,
    createSequence: createAnimationSequence,
    
    // Header animations
    animateHeader,
    animateH1FromSides,
    animateContentFromBottom,
    
    // Utility functions
    killAnimations,
    resetElements,
    createScrollAnimation,
    batchAnimate,
    killAllAnimations,
    
    // Access to tracked timelines
    timelines: timelinesRef.current,
  };
};

/**
 * Hook specifically for header animations
 * Provides a simplified interface for common header animation patterns
 */
export const useHeaderAnimations = () => {
  const { animateHeader, animateH1FromSides, animateContentFromBottom } = useAnimations();

  /**
   * Animate a complete header section with default settings
   */
  const animateHeaderSection = useCallback((containerRef, options = {}) => {
    if (!containerRef.current) return null;

    const container = containerRef.current;
    const h1 = container.querySelector('h1');
    const paragraph = container.querySelector('p');
    const buttons = container.querySelectorAll('button, a[class*="button"], a[class*="btn"]');

    const selectors = {
      h1,
      paragraph,
      buttons: Array.from(buttons),
      container,
    };

    return animateHeader(selectors, options);
  }, [animateHeader]);

  /**
   * Animate header with custom selectors
   */
  const animateCustomHeader = useCallback((selectors, options = {}) => {
    return animateHeader(selectors, options);
  }, [animateHeader]);

  /**
   * Split text animation for H1 elements
   */
  const animateH1Split = useCallback((h1Element, options = {}) => {
    if (!h1Element) return null;

    // For split text animation, we'll need to split the text into spans
    const text = h1Element.textContent;
    const words = text.split(' ');
    
    // Clear original content
    h1Element.innerHTML = '';
    
    // Create spans for each word
    const wordSpans = words.map((word, index) => {
      const span = document.createElement('span');
      span.textContent = word;
      span.style.display = 'inline-block';
      span.style.marginRight = '0.25em';
      h1Element.appendChild(span);
      return span;
    });

    // Animate words from alternating sides
    const leftWords = wordSpans.filter((_, index) => index % 2 === 0);
    const rightWords = wordSpans.filter((_, index) => index % 2 === 1);

    return animateH1FromSides(leftWords, rightWords, options);
  }, [animateH1FromSides]);

  return {
    animateHeaderSection,
    animateCustomHeader,
    animateH1Split,
    animateH1FromSides,
    animateContentFromBottom,
  };
};

/**
 * Hook for scroll-triggered animations
 */
export const useScrollAnimations = () => {
  const { createScrollAnimation } = useAnimations();

  /**
   * Create multiple scroll animations
   */
  const createScrollAnimations = useCallback((animationConfigs) => {
    const timelines = animationConfigs.map(config => {
      const { trigger, elements, animationType, options } = config;
      return createScrollAnimation(trigger, elements, animationType, options);
    });

    return timelines;
  }, [createScrollAnimation]);

  return {
    createScrollAnimation,
    createScrollAnimations,
  };
};

export default useAnimations;
