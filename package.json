{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.2", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-list": "^2.3.0", "gsap": "^3.13.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-i18next": "^15.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/vite": "^4.1.8", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.3.4", "vite": "^6.3.5"}}