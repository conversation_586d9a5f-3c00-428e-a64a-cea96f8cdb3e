import React, { useRef } from 'react';
import { useGSAP } from '@gsap/react';
import { gsapAnimation, slideIn, textVariant, headerVariants } from '@/lib/animations';
import gsap from 'gsap';

const AnimationTest = () => {
  const containerRef = useRef(null);
  const slideLeftRef = useRef(null);
  const slideRightRef = useRef(null);
  const slideUpRef = useRef(null);
  const slideDownRef = useRef(null);
  const textRef = useRef(null);
  const headerRef = useRef(null);

  useGSAP(() => {
    // Test slide animations
    const leftSlide = slideIn('left', 0, 1);
    const rightSlide = slideIn('right', 0.2, 1);
    const upSlide = slideIn('up', 0.4, 1);
    const downSlide = slideIn('down', 0.6, 1);
    const textAnim = textVariant(0.8);
    const headerAnim = headerVariants.h1FromSides();

    // Apply animations using gsapAnimation function
    gsapAnimation(slideLeftRef.current, leftSlide.hidden, leftSlide.show);
    gsapAnimation(slideRightRef.current, rightSlide.hidden, rightSlide.show);
    gsapAnimation(slideUpRef.current, upSlide.hidden, upSlide.show);
    gsapAnimation(slideDownRef.current, downSlide.hidden, downSlide.show);
    gsapAnimation(textRef.current, textAnim.hidden, textAnim.show);
    gsapAnimation(headerRef.current, headerAnim.hidden, headerAnim.show);

  }, { scope: containerRef });

  return (
    <div ref={containerRef} className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-4xl font-bold text-center mb-12">GSAP Animation System Test</h1>
        
        {/* Slide Animations */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div 
            ref={slideLeftRef}
            className="bg-blue-500 text-white p-6 rounded-lg text-center"
          >
            <h3 className="text-xl font-semibold">Slide from Left</h3>
            <p>This box slides in from the left side</p>
          </div>
          
          <div 
            ref={slideRightRef}
            className="bg-green-500 text-white p-6 rounded-lg text-center"
          >
            <h3 className="text-xl font-semibold">Slide from Right</h3>
            <p>This box slides in from the right side</p>
          </div>
          
          <div 
            ref={slideUpRef}
            className="bg-purple-500 text-white p-6 rounded-lg text-center"
          >
            <h3 className="text-xl font-semibold">Slide from Up</h3>
            <p>This box slides in from the top</p>
          </div>
          
          <div 
            ref={slideDownRef}
            className="bg-red-500 text-white p-6 rounded-lg text-center"
          >
            <h3 className="text-xl font-semibold">Slide from Down</h3>
            <p>This box slides in from the bottom</p>
          </div>
        </div>

        {/* Text Animation */}
        <div 
          ref={textRef}
          className="bg-yellow-400 p-8 rounded-lg text-center"
        >
          <h2 className="text-2xl font-bold">Text Variant Animation</h2>
          <p className="mt-2">This text animates with a spring effect</p>
        </div>

        {/* Header Animation */}
        <div 
          ref={headerRef}
          className="bg-indigo-600 text-white p-8 rounded-lg text-center"
        >
          <h2 className="text-3xl font-bold">Header Animation Test</h2>
          <p className="mt-4">This demonstrates the header animation variant</p>
        </div>

        {/* Instructions */}
        <div className="bg-gray-200 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Test Instructions:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Scroll down to trigger animations</li>
            <li>Each element should animate when it comes into view</li>
            <li>Animations should only play once (due to ScrollTrigger once: true)</li>
            <li>Check different screen sizes for responsiveness</li>
            <li>Verify smooth performance across different devices</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AnimationTest;
