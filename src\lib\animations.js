import { gsap } from 'gsap';

/**
 * GSAP Animation Utility System
 * A comprehensive, reusable animation framework for React components
 */

// Default animation configurations
const DEFAULT_CONFIG = {
  duration: 1,
  ease: 'power2.out',
  delay: 0,
  stagger: 0.1,
};

// Animation type configurations
const ANIMATION_TYPES = {
  slideLeft: {
    from: { x: -100, opacity: 0 },
    to: { x: 0, opacity: 1 },
  },
  slideRight: {
    from: { x: 100, opacity: 0 },
    to: { x: 0, opacity: 1 },
  },
  slideUp: {
    from: { y: 100, opacity: 0 },
    to: { y: 0, opacity: 1 },
  },
  slideDown: {
    from: { y: -100, opacity: 0 },
    to: { y: 0, opacity: 1 },
  },
  scale: {
    from: { scale: 0, opacity: 0 },
    to: { scale: 1, opacity: 1 },
  },
  fade: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  scaleUp: {
    from: { scale: 0.8, opacity: 0 },
    to: { scale: 1, opacity: 1 },
  },
  scaleDown: {
    from: { scale: 1.2, opacity: 0 },
    to: { scale: 1, opacity: 1 },
  },
};

/**
 * Core animation function
 * @param {HTMLElement|HTMLElement[]|string} elements - Elements to animate
 * @param {string} animationType - Type of animation from ANIMATION_TYPES
 * @param {Object} config - Animation configuration
 * @returns {gsap.core.Timeline} GSAP timeline
 */
export const animate = (elements, animationType, config = {}) => {
  const animConfig = ANIMATION_TYPES[animationType];
  if (!animConfig) {
    console.warn(`Animation type "${animationType}" not found`);
    return null;
  }

  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const timeline = gsap.timeline();

  // Set initial state
  gsap.set(elements, animConfig.from);

  // Animate to final state
  timeline.to(elements, {
    ...animConfig.to,
    duration: finalConfig.duration,
    ease: finalConfig.ease,
    delay: finalConfig.delay,
    stagger: finalConfig.stagger,
  });

  return timeline;
};

/**
 * Create a sequential animation timeline
 * @param {Array} animationSequence - Array of animation objects
 * @returns {gsap.core.Timeline} GSAP timeline
 */
export const createSequence = (animationSequence) => {
  const masterTimeline = gsap.timeline();

  animationSequence.forEach((animationStep, index) => {
    const { elements, type, config = {}, position } = animationStep;
    
    const stepTimeline = animate(elements, type, config);
    if (stepTimeline) {
      if (position !== undefined) {
        masterTimeline.add(stepTimeline, position);
      } else {
        masterTimeline.add(stepTimeline);
      }
    }
  });

  return masterTimeline;
};

/**
 * Header-specific animation presets
 */
export const headerAnimations = {
  /**
   * Animate H1 elements from left and right simultaneously
   * @param {HTMLElement|string} leftElement - Element to animate from left
   * @param {HTMLElement|string} rightElement - Element to animate from right (optional)
   * @param {Object} config - Animation configuration
   * @returns {gsap.core.Timeline} GSAP timeline
   */
  animateH1FromSides: (leftElement, rightElement = null, config = {}) => {
    const timeline = gsap.timeline();
    const finalConfig = { ...DEFAULT_CONFIG, duration: 1.2, ...config };

    if (rightElement) {
      // Split H1 animation - left and right parts
      gsap.set(leftElement, { x: -100, opacity: 0 });
      gsap.set(rightElement, { x: 100, opacity: 0 });

      timeline
        .to(leftElement, {
          x: 0,
          opacity: 1,
          duration: finalConfig.duration,
          ease: finalConfig.ease,
        })
        .to(rightElement, {
          x: 0,
          opacity: 1,
          duration: finalConfig.duration,
          ease: finalConfig.ease,
        }, '<'); // Start at the same time as previous animation
    } else {
      // Single H1 element - animate from center with scale
      gsap.set(leftElement, { scale: 0.8, opacity: 0 });
      timeline.to(leftElement, {
        scale: 1,
        opacity: 1,
        duration: finalConfig.duration,
        ease: finalConfig.ease,
      });
    }

    return timeline;
  },

  /**
   * Animate paragraph and button elements from bottom after H1 completes
   * @param {HTMLElement[]|string} elements - Elements to animate
   * @param {Object} config - Animation configuration
   * @returns {gsap.core.Timeline} GSAP timeline
   */
  animateContentFromBottom: (elements, config = {}) => {
    const finalConfig = { 
      ...DEFAULT_CONFIG, 
      duration: 0.8, 
      stagger: 0.2,
      ...config 
    };

    return animate(elements, 'slideUp', finalConfig);
  },

  /**
   * Complete header animation sequence
   * @param {Object} selectors - Object containing element selectors
   * @param {Object} config - Animation configuration
   * @returns {gsap.core.Timeline} GSAP timeline
   */
  animateHeaderSequence: (selectors, config = {}) => {
    const { h1, paragraph, buttons, container } = selectors;
    const masterTimeline = gsap.timeline();
    const finalConfig = { ...DEFAULT_CONFIG, ...config };

    // Step 1: Animate H1
    const h1Timeline = headerAnimations.animateH1FromSides(h1, null, {
      duration: 1.2,
      ease: 'power3.out',
    });

    // Step 2: Animate paragraph and buttons after H1 completes
    const contentElements = [];
    if (paragraph) contentElements.push(paragraph);
    if (buttons) {
      if (Array.isArray(buttons)) {
        contentElements.push(...buttons);
      } else {
        contentElements.push(buttons);
      }
    }

    const contentTimeline = headerAnimations.animateContentFromBottom(
      contentElements,
      {
        duration: 0.8,
        stagger: 0.15,
        ease: 'power2.out',
      }
    );

    // Add animations to master timeline
    masterTimeline
      .add(h1Timeline)
      .add(contentTimeline, '+=0.3'); // Start 0.3s after H1 completes

    return masterTimeline;
  },
};

/**
 * Utility functions for common animation tasks
 */
export const animationUtils = {
  /**
   * Kill all animations on elements
   * @param {HTMLElement|HTMLElement[]|string} elements - Elements to kill animations on
   */
  killAnimations: (elements) => {
    gsap.killTweensOf(elements);
  },

  /**
   * Reset elements to their initial state
   * @param {HTMLElement|HTMLElement[]|string} elements - Elements to reset
   */
  resetElements: (elements) => {
    gsap.set(elements, { clearProps: 'all' });
  },

  /**
   * Create a scroll-triggered animation
   * @param {HTMLElement|string} trigger - Element that triggers the animation
   * @param {HTMLElement|HTMLElement[]|string} elements - Elements to animate
   * @param {string} animationType - Type of animation
   * @param {Object} config - Animation configuration
   * @returns {gsap.core.Timeline} GSAP timeline
   */
  createScrollAnimation: (trigger, elements, animationType, config = {}) => {
    // Note: This would require ScrollTrigger plugin for full functionality
    // For now, we'll create a basic intersection observer implementation
    const timeline = animate(elements, animationType, config);
    
    if (typeof trigger === 'string') {
      trigger = document.querySelector(trigger);
    }

    if (trigger && timeline) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              timeline.play();
              observer.unobserve(entry.target);
            }
          });
        },
        { threshold: 0.1 }
      );

      observer.observe(trigger);
    }

    return timeline;
  },

  /**
   * Batch animate multiple elements with different animations
   * @param {Array} animationBatch - Array of animation configurations
   * @returns {gsap.core.Timeline} GSAP timeline
   */
  batchAnimate: (animationBatch) => {
    const timeline = gsap.timeline();

    animationBatch.forEach((item) => {
      const { elements, type, config, position } = item;
      const stepTimeline = animate(elements, type, config);
      
      if (stepTimeline) {
        if (position !== undefined) {
          timeline.add(stepTimeline, position);
        } else {
          timeline.add(stepTimeline);
        }
      }
    });

    return timeline;
  },
};

// Export animation types for reference
export { ANIMATION_TYPES, DEFAULT_CONFIG };
