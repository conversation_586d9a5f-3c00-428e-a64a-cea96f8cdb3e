import gsap from "gsap";
import { ScrollTrigger } from "gsap/all";

gsap.registerPlugin(ScrollTrigger)

export const gsapAnimation = (target, animationFromProps, animationToProps) => {
    gsap.fromTo(target, {
        ...animationFromProps
    }, {
        ...animationToProps,
        scrollTrigger: {
            trigger: target,
            start: 'top 80%',
            toggleActions: 'play none none none',
            once: true,
        }
    })
}

// Animation variants similar to Framer Motion pattern
export const slideIn = (direction, delay = 0, duration = 1) => ({
    hidden: {
        x: direction === 'left' ? '-100%' : direction === 'right' ? '100%' : 0,
        y: direction === 'up' ? '100%' : direction === 'down' ? '-100%' : 0,
        opacity: 0,
    },
    show: {
        x: 0,
        y: 0,
        opacity: 1,
        duration,
        delay,
        ease: 'power2.out',
    },
});

export const textVariant = (delay = 0) => ({
    hidden: {
        y: 50,
        opacity: 0,
    },
    show: {
        y: 0,
        opacity: 1,
        duration: 1.25,
        delay,
        ease: 'power3.out',
    },
});

// Header-specific animations
export const headerVariants = {
    h1FromSides: () => ({
        hidden: {
            scale: 0.8,
            opacity: 0,
        },
        show: {
            scale: 1,
            opacity: 1,
            duration: 1.2,
            ease: 'power3.out',
        },
    }),

    contentFromBottom: () => ({
        hidden: {
            y: 100,
            opacity: 0,
        },
        show: {
            y: 0,
            opacity: 1,
            duration: 0.8,
            ease: 'power2.out',
            stagger: 0.15,
        },
    }),
};