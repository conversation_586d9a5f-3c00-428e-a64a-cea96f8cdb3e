import gsap from "gsap";
import { ScrollTrigger } from "gsap/all";

gsap.registerPlugin(ScrollTrigger);

/**
 * GSAP Animation Variants System
 * Similar to Framer Motion variants but using GSAP with ScrollTrigger
 */

// Core animation function with ScrollTrigger support
export const gsapAnimation = (target, animationFromProps, animationToProps, scrollTriggerConfig = {}) => {
    const defaultScrollConfig = {
        trigger: target,
        start: 'top 80%',
        toggleActions: 'play none none none',
        once: true,
        ...scrollTriggerConfig
    };

    return gsap.fromTo(target, {
        ...animationFromProps
    }, {
        ...animationToProps,
        scrollTrigger: defaultScrollConfig
    });
};

// Navigation variants (similar to navVariants from Framer Motion)
export const navVariants = {
    hidden: {
        opacity: 0,
        y: -50,
    },
    show: {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "back.out(1.7)",
        delay: 1,
    },
};

// Slide animations (similar to slideIn from Framer Motion)
export const slideIn = (direction, delay = 0, duration = 1) => ({
    hidden: {
        x: direction === 'left' ? '-100%' : direction === 'right' ? '100%' : 0,
        y: direction === 'up' ? '100%' : direction === 'down' ? '-100%' : 0,
        opacity: 0,
    },
    show: {
        x: 0,
        y: 0,
        opacity: 1,
        duration,
        delay,
        ease: 'power2.out',
    },
});

// Text variants (similar to textVariant from Framer Motion)
export const textVariant = (delay = 0) => ({
    hidden: {
        y: 50,
        opacity: 0,
    },
    show: {
        y: 0,
        opacity: 1,
        duration: 1.25,
        delay,
        ease: 'power3.out',
    },
});

// Text variant 2 (similar to textVariant2 from Framer Motion)
export const textVariant2 = {
    hidden: {
        opacity: 0,
        y: 20,
    },
    show: {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: 'power2.out',
    },
};

// Fade animations (similar to fadeIn from Framer Motion)
export const fadeIn = (direction, delay = 0, duration = 1) => ({
    hidden: {
        x: direction === 'left' ? 100 : direction === 'right' ? -100 : 0,
        y: direction === 'up' ? 100 : direction === 'down' ? -100 : 0,
        opacity: 0,
    },
    show: {
        x: 0,
        y: 0,
        opacity: 1,
        duration,
        delay,
        ease: 'power2.out',
    },
});

// Planet/rotate variants (similar to planetVariants from Framer Motion)
export const planetVariants = (direction) => ({
    hidden: {
        x: direction === 'left' ? '-100%' : '100%',
        rotation: 120,
        opacity: 0,
    },
    show: {
        x: 0,
        rotation: 0,
        opacity: 1,
        duration: 1.8,
        delay: 0.5,
        ease: 'back.out(1.7)',
    },
});

// Zoom animations (similar to zoomIn from Framer Motion)
export const zoomIn = (delay = 0, duration = 1) => ({
    hidden: {
        scale: 0,
        opacity: 0,
    },
    show: {
        scale: 1,
        opacity: 1,
        duration,
        delay,
        ease: 'back.out(1.7)',
    },
});

// Footer variants (similar to footerVariants from Framer Motion)
export const footerVariants = {
    hidden: {
        opacity: 0,
        y: 50,
    },
    show: {
        opacity: 1,
        y: 0,
        duration: 1,
        delay: 0.5,
        ease: 'back.out(1.7)',
    },
};

// Stagger container (similar to staggerContainer from Framer Motion)
export const staggerContainer = (staggerChildren = 0.1, delayChildren = 0) => ({
    hidden: {},
    show: {
        stagger: staggerChildren,
        delay: delayChildren,
    },
});

// Text container (similar to textContainer from Framer Motion)
export const textContainer = {
    hidden: {
        opacity: 0,
    },
    show: (staggerValue = 0.1, delayValue = 0.1) => ({
        opacity: 1,
        stagger: staggerValue,
        delay: delayValue,
    }),
};

/**
 * Header-specific animation variants for Hero sections
 */
export const headerVariants = {
    // H1 animation from left and right sides
    h1FromSides: {
        hidden: {
            scale: 0.8,
            opacity: 0,
        },
        show: {
            scale: 1,
            opacity: 1,
            duration: 1.2,
            ease: 'power3.out',
        },
    },

    // Content animation from bottom (paragraphs and buttons)
    contentFromBottom: {
        hidden: {
            y: 100,
            opacity: 0,
        },
        show: {
            y: 0,
            opacity: 1,
            duration: 0.8,
            ease: 'power2.out',
            stagger: 0.15,
        },
    },

    // Complete header sequence
    headerSequence: {
        hidden: {},
        show: {
            stagger: 0.3, // Delay between H1 and content
        },
    },
};

/**
 * Utility functions for applying animations using variants
 */
export const animationUtils = {
    /**
     * Apply animation variant to elements
     * @param {HTMLElement|HTMLElement[]|string} elements - Elements to animate
     * @param {Object} variant - Animation variant object
     * @param {Object} scrollTriggerConfig - ScrollTrigger configuration
     * @returns {gsap.core.Tween|gsap.core.Timeline} GSAP animation
     */
    applyVariant: (elements, variant, scrollTriggerConfig = {}) => {
        if (!variant.hidden || !variant.show) {
            console.warn('Variant must have both hidden and show properties');
            return null;
        }

        return gsapAnimation(elements, variant.hidden, variant.show, scrollTriggerConfig);
    },

    /**
     * Apply stagger animation to multiple elements
     * @param {HTMLElement[]|string} elements - Elements to animate
     * @param {Object} variant - Animation variant object
     * @param {Object} scrollTriggerConfig - ScrollTrigger configuration
     * @returns {gsap.core.Timeline} GSAP timeline
     */
    applyStaggerVariant: (elements, variant, scrollTriggerConfig = {}) => {
        if (!variant.hidden || !variant.show) {
            console.warn('Variant must have both hidden and show properties');
            return null;
        }

        const timeline = gsap.timeline();
        const elementsArray = typeof elements === 'string' ?
            document.querySelectorAll(elements) :
            Array.isArray(elements) ? elements : [elements];

        // Set initial state
        gsap.set(elementsArray, variant.hidden);

        // Animate with stagger
        timeline.to(elementsArray, {
            ...variant.show,
            scrollTrigger: {
                trigger: elementsArray[0],
                start: 'top 80%',
                toggleActions: 'play none none none',
                once: true,
                ...scrollTriggerConfig
            }
        });

        return timeline;
    },

    /**
     * Create header animation sequence
     * @param {Object} elements - Object containing h1, paragraph, and buttons elements
     * @param {Object} scrollTriggerConfig - ScrollTrigger configuration
     * @returns {gsap.core.Timeline} GSAP timeline
     */
    animateHeaderSequence: (elements, scrollTriggerConfig = {}) => {
        const { h1, paragraph, buttons, container } = elements;
        const timeline = gsap.timeline();

        // Set initial states
        if (h1) gsap.set(h1, headerVariants.h1FromSides.hidden);
        if (paragraph) gsap.set(paragraph, headerVariants.contentFromBottom.hidden);
        if (buttons) gsap.set(buttons, headerVariants.contentFromBottom.hidden);

        // H1 animation
        if (h1) {
            timeline.to(h1, headerVariants.h1FromSides.show);
        }

        // Content animation (paragraph and buttons) with delay
        const contentElements = [];
        if (paragraph) contentElements.push(paragraph);
        if (buttons) {
            if (Array.isArray(buttons)) {
                contentElements.push(...buttons);
            } else {
                contentElements.push(buttons);
            }
        }

        if (contentElements.length > 0) {
            timeline.to(contentElements, {
                ...headerVariants.contentFromBottom.show,
            }, '+=0.3'); // Start 0.3s after H1
        }

        // Add ScrollTrigger if needed
        if (container && Object.keys(scrollTriggerConfig).length > 0) {
            ScrollTrigger.create({
                trigger: container,
                start: 'top 80%',
                toggleActions: 'play none none none',
                once: true,
                ...scrollTriggerConfig,
                animation: timeline,
            });
        }

        return timeline;
    },

    /**
     * Kill all animations on elements
     * @param {HTMLElement|HTMLElement[]|string} elements - Elements to kill animations on
     */
    killAnimations: (elements) => {
        gsap.killTweensOf(elements);
    },

    /**
     * Reset elements to their initial state
     * @param {HTMLElement|HTMLElement[]|string} elements - Elements to reset
     */
    resetElements: (elements) => {
        gsap.set(elements, { clearProps: 'all' });
    },
};
